<?php

namespace App\Services;

use App\Models\Common;
use App\Models\Merchant;
use App\Models\MerchantToken;
use App\Models\MerchantAccountLog;
use App\Models\O2oErrandOrder;
use App\Models\SystemConfig;
use App\Models\User;
use App\Models\UserAddress;
use App\Models\UserAccount;
use App\Models\UserAccountFlow;
use App\Models\Site;
use App\Models\Pricing;
use App\Models\Region;
use App\Services\Amap\GaodeService;
use App\Services\UserService;
use App\Services\MapService;
use App\Services\CommonService;
use App\Services\O2oErrandOrderService;
use App\Exceptions\BusinessException;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Hash;
use Carbon\Carbon;

class QingyunService
{
    protected string $appKey;
    protected string $appSecret;

    // 青云订单来源Code
    const TRADE_ORDER_SOURCE_JINGDONG = 300;      // 京东
    const TRADE_ORDER_SOURCE_MEITUAN = 100;       // 美团
    const TRADE_ORDER_SOURCE_ELEME = 200;         // 饿了么
    const TRADE_ORDER_SOURCE_QINGYUN = 400;       // 青云自营
    const TRADE_ORDER_SOURCE_YOUZAN = 600;        // 有赞

    const TRADE_ORDER_SOURCE_DOUYIN = 700;        // 抖音
    const TRADE_ORDER_SOURCE_TAOBAO_MAICAI = 800; // 淘宝买菜

    // 青云商品经营品类Code
    const CATEGORY_FOOD = 100;                    // 美食
    const CATEGORY_FRESH = 104;                   // 生鲜果蔬
    const CATEGORY_MEDICINE = 108;                // 医药健康
    const CATEGORY_SUPERMARKET = 105;             // 超市百货
    const CATEGORY_FLOWER = 103;                  // 鲜花绿植
    const CATEGORY_CAKE = 102;                    // 烘焙蛋糕
    const CATEGORY_DRINK = 101;                   // 饮品奶茶
    const CATEGORY_OTHER = 999;                  // 其他

    // 青云接口返回结果Code
    const RESULT_SUCCESS = 0;                   // 成功
    const RESULT_SYSTEM_ERROR = 1;              // 系统异常
    const RESULT_PARAM_ERROR = 2;               // 缺少参数，或参数格式错误
    const RESULT_SIGN_ERROR = 3;                // 签名验证失败
    const RESULT_UNAUTHORIZED = 4;              // 未授权或授权过期
    const RESULT_RATE_LIMIT = 5;                // 接口流控
    const RESULT_OTHER_ERROR = 9;               // 其他原因
    const RESULT_NO_CAPACITY = 10;              // 该地区暂无运力

    // 订单相关错误码
    const RESULT_ORDER_NOT_EXIST = 101;         // 订单不存在
    const RESULT_ORDER_COMPLETED = 102;         // 订单已完成，不能取消
    const RESULT_SERVICE_NOT_OPEN = 103;        // 门店未开通所选服务产品
    const RESULT_OUT_OF_RANGE = 104;            // 送货地址超出配送范围
    const RESULT_NO_APPOINTMENT = 105;          // 预约时间内无法完成履约
    const RESULT_NO_RIDER = 106;                // 运力紧张，无法创建订单
    const RESULT_INSUFFICIENT_BALANCE = 107;    // 账户余额不足或扣款失败
    const RESULT_NO_APPOINTMENT_SUPPORT = 108;  // 暂不支持预约单
    const RESULT_NO_REVERSE_ORDER = 109;        // 暂不支持逆向发单
    const RESULT_RIDER_ACCEPTED = 110;          // 骑手已接单，无法添加小费
    const RESULT_TIP_LIMIT = 111;               // 小费金额已至上限，无法继续添加
    const RESULT_NO_RIDER_LOCATION = 112;       // 骑手目前没有位置信息，请稍后重试

    // 回调相关错误码
    const RESULT_CARRIER_MISMATCH = 301;        // 配送商不匹配
    const RESULT_ORDER_NO_MISMATCH = 302;       // 配送商物流单号不匹配
    const RESULT_WAYBILL_NOT_EXIST = 303;       // 运单不存在

    // 骑手相关错误码
    const RESULT_RIDER_INFO_ERROR = 501;        // 骑手信息错误，请扫正确的骑手码
    const RESULT_RIDER_ORDER_LIMIT = 502;       // 骑手接单数已超上限
    const RESULT_RIDER_CANNOT_ACCEPT = 503;     // 骑手无法接单
    const RESULT_FUNCTION_NOT_OPEN = 504;       // 请联系配送商客服开通功能后使用

    // 门店相关错误码
    const RESULT_STORE_CREATE_FAILED = 601;     // 暂时无法创建门店或修改门店信息，需要稍后重试
    const RESULT_STORE_NO_CHANGE = 602;         // 此次变更未修改任何门店信息

    // 青云订单状态Code - 配送商订单状态（新枚举）
    const QY_STATUS_CREATED = 10;               // 已创建(待接单)
    const QY_STATUS_ACCEPTED = 20;              // 骑手已接单
    const QY_STATUS_ARRIVED_STORE = 25;         // 已到店
    const QY_STATUS_PICKED_UP = 30;             // 已取货
    const QY_STATUS_DELIVERED = 50;             // 已送达
    const QY_STATUS_CANCELLED = 99;             // 已取消

    // 系统订单状态到青云状态的映射
    const STATUS_TO_QY_MAP = [
        O2oErrandOrder::STATUS_WAITING_PAY => self::QY_STATUS_CREATED,      // 待支付 -> 已创建(待接单)
        O2oErrandOrder::STATUS_PAID => self::QY_STATUS_CREATED,             // 待接单 -> 已创建(待接单)
        O2oErrandOrder::STATUS_PICKUP => self::QY_STATUS_ACCEPTED,          // 待取货 -> 骑手已接单
        O2oErrandOrder::STATUS_ARRIVE_PICKUP_POINT => self::QY_STATUS_ARRIVED_STORE, // 到达取货点 -> 已到店
        O2oErrandOrder::STATUS_DELIVERY => self::QY_STATUS_PICKED_UP,       // 派送中 -> 已取货
        O2oErrandOrder::STATUS_FINISH => self::QY_STATUS_DELIVERED,         // 已完成 -> 已送达
        O2oErrandOrder::STATUS_CANCEL => self::QY_STATUS_CANCELLED,         // 已取消 -> 已取消
    ];

    protected string $developerId;
    protected string $secret;

    public function __construct()
    {

        // 青云调用协议配置
        $this->developerId = config('qingyun.developer_id', 'qingyun_developer_id');
        $this->secret = config('qingyun.secret', 'qingyun_secret');
    }

    /**
     * 创建或修改配送商门店
     *
     * @param array $data 门店数据
     * @return array 处理结果
     * @throws \Exception
     */
    public function createOrUpdateStore(array $data): array
    {
        Log::channel('qingyun')->info('青云创建/修改配送商门店请求', $data);

        try {

            // 开始数据库事务
            DB::beginTransaction();

            // 检查是否已存在该门店
            $existingMerchant = $this->findExistingMerchant($data);

            if ($existingMerchant) {
                // 更新现有门店
                $result = $this->updateExistingStore($existingMerchant, $data);
            } else {
                // 创建新门店
                $result = $this->createNewStore($data);
            }

            DB::commit();

            Log::channel('qingyun')->info('青云门店操作成功', [
                'operation' => $existingMerchant ? 'update' : 'create',
                'merchant_id' => $result['merchant_id'],
                'user_id' => $result['user_id']
            ]);

            return [
                'success' => true,
                'message' => $existingMerchant ? '门店更新成功' : '门店创建成功',
                'data' => $result
            ];

        } catch (\Exception $e) {
            DB::rollBack();

            Log::channel('qingyun')->error('青云门店操作失败', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'data' => $data
            ]);

            throw $e;
        }
    }


    /**
     * 查找已存在的商家
     *
     * @param array $data
     * @return Merchant|null
     */
    private function findExistingMerchant(array $data): ?Merchant
    {
        return Merchant::where('merchant_type', 'qingyun')
            ->where('phone', $data['contactPhone'])
            ->first();
    }

    /**
     * 创建新门店
     *
     * @param array $data
     * @return array
     * @throws \Exception
     */
    private function createNewStore(array $data): array
    {
        // 检查该手机号是否已有用户账号
        $user = User::where('phone', $data['contactPhone'])->first();

        // 如果没有用户账号，则创建一个
        if (!$user) {
            $userService = app(UserService::class);
            $user = $userService->registerUser(
                $data['contactPhone'],
                Hash::make('123456'), // 默认密码
                '', // 空邀请码
                \App\Models\SystemConfig::PlatformPT // 平台标识为跑腿平台
            );

            Log::channel('qingyun')->info("为青云商家创建关联用户账号成功", [
                'user_id' => $user->id,
                'phone' => $data['contactPhone']
            ]);
        }

        // 解析地址信息（从shopAddress中提取省市区信息）
        $addressInfo = $this->parseAddress($data['shopAddress']);

        // 创建商家账户
        $merchant = Merchant::create([
            'shop_name' => $data['shopName'],
            'phone' => $data['contactPhone'],
            'password' => Hash::make('123456'),
            'province' => $addressInfo['province_code'],
            'city' => $addressInfo['city_code'],
            'district' => $addressInfo['district_code'],
            'city_code' => $addressInfo['city_code'] ?? $addressInfo['district_code'], // 兼容旧字段，优先使用区县编码
            'address' => $data['shopAddress'],
            'contact_name' => '',
            'email' => '',
            'merchant_type' => 'qingyun',
            'status' => 1, // 青云商家默认审核通过
            'balance' => 0,
            'user_id' => $user->id,
        ]);

        // 生成配送商门店ID
        $carrierShopId = 'QY_' . $merchant->id;

        return [
            'merchant_id' => $merchant->id,
            'user_id' => $user->id,
            'carrier_shop_id' => $carrierShopId,
            'operation' => 'create'
        ];
    }

    /**
     * 更新现有门店
     *
     * @param Merchant $merchant
     * @param array $data
     * @return array
     */
    private function updateExistingStore(Merchant $merchant, array $data): array
    {
        // 解析地址信息
        $addressInfo = $this->parseAddress($data['shopAddress']);

        // 更新商家信息
        $merchant->update([
            'shop_name' => $data['shopName'],
            'province' => $addressInfo['province_code'],
            'city' => $addressInfo['city_code'],
            'district' => $addressInfo['district_code'],
            'city_code' => $addressInfo['city_code'] ?? $addressInfo['district_code'], // 兼容旧字段，优先使用区县编码
            'address' => $data['shopAddress'],
        ]);


        // 生成配送商门店ID
        $carrierShopId = 'QY_' . $merchant->id;

        return [
            'merchant_id' => $merchant->id,
            'user_id' => $merchant->user_id,
            'carrier_shop_id' => $carrierShopId,
            'operation' => 'update'
        ];
    }


    /**
     * 解析地址信息，从详细地址中提取省市区信息
     * 参考 maiyatian 实现，返回地区名称和编码
     *
     * @param string $address
     * @return array
     */
    private function parseAddress(string $address): array
    {
        Log::channel('qingyun')->info('开始解析地址', ['address' => $address]);

        $province = '';
        $city = '';
        $district = '';
        $provinceCode = '';
        $cityCode = '';
        $districtCode = '';

        // 定义常见的省市区关键词
        $provinceKeywords = ['省', '自治区', '特别行政区', '市'];
        $cityKeywords = ['市', '地区', '州', '盟'];
        $districtKeywords = ['区', '县', '市', '旗'];

        // 提取省份信息
        foreach ($provinceKeywords as $keyword) {
            if (preg_match('/(.+?)' . $keyword . '/', $address, $matches)) {
                $provinceName = $matches[1] . $keyword;
                break;
            }
        }

        // 提取城市信息
        if (isset($provinceName)) {
            $remainingAddress = str_replace($provinceName, '', $address);
            foreach ($cityKeywords as $keyword) {
                if (preg_match('/(.+?)' . $keyword . '/', $remainingAddress, $matches)) {
                    $cityName = $matches[1] . $keyword;
                    break;
                }
            }
        }

        // 提取区县信息
        if (isset($cityName)) {
            $remainingAddress = str_replace($cityName, '', $remainingAddress);
            foreach ($districtKeywords as $keyword) {
                if (preg_match('/(.+?)' . $keyword . '/', $remainingAddress, $matches)) {
                    $districtName = $matches[1] . $keyword;
                    break;
                }
            }
        }

        // 如果没有提取到城市，但有省份和区县，尝试直接从区县推断
        if (!isset($cityName) && isset($provinceName) && isset($districtName)) {
            // 对于直辖市等情况，城市名可能就是省份名
            if (in_array($provinceName, ['北京市', '上海市', '天津市', '重庆市'])) {
                $cityName = $provinceName;
            }
        }

        // 构建搜索条件数组
        $searchConditions = [];
        if (isset($provinceName)) {
            $searchConditions['province'] = $provinceName;
        }
        if (isset($cityName)) {
            $searchConditions['city'] = $cityName;
        }
        if (isset($districtName)) {
            $searchConditions['district'] = $districtName;
        }

        Log::channel('qingyun')->info('地址解析结果', [
            'original_address' => $address,
            'extracted' => $searchConditions
        ]);

        if (empty($searchConditions)) {
            Log::channel('qingyun')->warning('无法从地址中提取省市区信息', ['address' => $address]);
            return [
                'province' => '',
                'city' => '',
                'district' => '',
                'province_code' => '',
                'city_code' => '',
                'district_code' => ''
            ];
        }

        try {
            // 一次查询获取所有匹配的地区
            $query = Region::query();
            $query->where(function($q) use ($searchConditions, $provinceName, $cityName, $districtName) {
                if (!empty($provinceName)) {
                    $q->orWhere(function($subQ) use ($provinceName) {
                        $subQ->where('name', 'like', "%{$provinceName}%")
                             ->where('level', 'province');
                    });
                }
                if (!empty($cityName)) {
                    $q->orWhere(function($subQ) use ($cityName) {
                        $subQ->where('name', 'like', "%{$cityName}%")
                             ->where('level', 'city');
                    });
                }
                if (!empty($districtName)) {
                    $q->orWhere(function($subQ) use ($districtName) {
                        $subQ->where('name', 'like', "%{$districtName}%")
                             ->where('level', 'district');
                    });
                }
            });

            $regions = $query->get();

            Log::channel('qingyun')->info('数据库查询到的地区', [
                'count' => $regions->count(),
                'regions' => $regions->toArray()
            ]);

            // 分别处理省市区
            foreach ($regions as $region) {
                switch ($region->level) {
                    case 'province':
                        if (empty($province) && isset($provinceName) && strpos($region->name, str_replace(['省', '自治区', '特别行政区'], '', $provinceName)) !== false) {
                            $province = $region->name;
                            $provinceCode = $region->code;
                        }
                        break;
                    case 'city':
                        if (empty($city) && isset($cityName) && strpos($region->name, str_replace(['市', '地区', '州', '盟'], '', $cityName)) !== false) {
                            $city = $region->name;
                            $cityCode = $region->code;
                        }
                        break;
                    case 'district':
                        if (empty($district) && isset($districtName) && strpos($region->name, str_replace(['区', '县', '市', '旗'], '', $districtName)) !== false) {
                            $district = $region->name;
                            $districtCode = $region->code;
                        }
                        break;
                }
            }

        } catch (\Exception $e) {
            Log::channel('qingyun')->error('查询地区信息时发生异常', [
                'error' => $e->getMessage(),
                'address' => $address
            ]);
        }

        $result = [
            'province' => $province,
            'city' => $city,
            'district' => $district,
            'province_code' => $provinceCode,
            'city_code' => $cityCode,
            'district_code' => $districtCode
        ];

        Log::channel('qingyun')->info('最终地址解析结果', $result);

        return $result;
    }

    /**
     * 根据配送商门店ID查找商家
     *
     * @param string $carrierShopId
     * @return Merchant|null
     */
    private function findByCarrierMerchantId(string $carrierShopId): ?Merchant
    {
        // 青云的门店ID格式为 QY_{merchant_id}
        if (strpos($carrierShopId, 'QY_') === 0) {
            $merchantId = substr($carrierShopId, 3);
            return Merchant::where('id', $merchantId)
                ->where('merchant_type', 'qingyun')
                ->first();
        }
        return null;
    }

    /**
     * 询价接口 - 按照 maiyatian 模式实现，支持已有订单查询
     *
     * @param array $data 询价请求数据
     * @return array 询价结果
     * @throws \Exception
     */
    public function valuatingWithOrderLookup(array $data): array
    {
        Log::channel('qingyun')->info('青云询价请求（新模式）', $data);

        try {
            // 1. 查找用户ID
            $merchant = $this->findByCarrierMerchantId($data['carrierShopId']);
            if (!$merchant) {
                return [
                    'code' => self::RESULT_PARAM_ERROR,
                    'message' => '配送商ID不存在',
                    'data' => null
                ];
            }

            $userId = $merchant->user_id;

            // 2. 查找是否已存在订单
            $order = O2oErrandOrder::where('out_order_no', $data['orderId'])
                ->where('app_key', O2oErrandOrder::APP_KEY_QY)
                ->first();
            // 3. 如果订单已存在，返回订单信息
            if ($order) {
                $result = $this->formatExistingOrderResult($order);

                Log::channel('qingyun')->info('青云询价成功（已有订单）', [
                    'order_id' => $order->id,
                    'order_no' => $order->order_no,
                    'result' => $result
                ]);

                return [
                    'code' => self::RESULT_SUCCESS,
                    'message' => '成功',
                    'data' => $result
                ];
            }

            // 4. 如果订单不存在，使用 O2oErrandOrderService 进行询价
            $result = $this->performNewOrderValuating($userId, $data);

            Log::channel('qingyun')->info('青云询价成功（新订单）', [
                'user_id' => $userId,
                'result' => $result
            ]);

            return [
                'code' => self::RESULT_SUCCESS,
                'message' => '成功',
                'data' => $result
            ];

        } catch (\Exception $e) {
            Log::channel('qingyun')->error('青云询价失败', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'data' => $data
            ]);

            return [
                'code' => self::RESULT_SYSTEM_ERROR,
                'message' => '系统错误: ' . $e->getMessage(),
                'data' => null
            ];
        }
    }

    /**
     * 格式化已有订单的返回结果
     *
     * @param O2oErrandOrder $order
     * @return array
     */
    private function formatExistingOrderResult(O2oErrandOrder $order): array
    {
        // 计算实际支付金额（与 performNewOrderValuating 逻辑保持一致）
        $actualFee = floatval(fentoyuan($order->actual_amount + $order->gratuity + $order->goods_protected_price));

        return [
            'predictDeliveryTime' => $order->estimated_delivery_time->timestamp,
            'actualFee' => $actualFee,
            'deliveryFee' => $actualFee,
            'deliveryDistance' => $order->distance,
            'discountFee' => 0.0,
            'insuredFee' => 0.0,
        ];
    }

    /**
     * 执行新订单询价
     *
     * @param int $userId
     * @param array $data
     * @return array
     * @throws \Exception
     */
    private function performNewOrderValuating(int $userId, array $data): array
    {
        $service = new O2oErrandOrderService();

        // 转换经纬度格式（青云传入的是整数，需要转换为小数）
        $senderLng = $data["senderLng"]/1000000;
        $senderLat = $data["senderLat"]/1000000;
        $recipientLng = $data["recipientLng"]/1000000;
        $recipientLat = $data["recipientLat"]/1000000;

        // 准备预约时间
        $appointmentTime = isset($data['appointmentTime']) && $data['appointmentTime'] > 0
            ? date('Y-m-d H:i:s', $data['appointmentTime'])
            : null;

        // 调用 O2oErrandOrderService 的 preOrder 方法
        $preOrderParams = [
            "type" => 1, // 普通配送
            "coupon_id" => 0,
            "gratuity" => 0, // 青云暂不支持小费
            "appointment_time" => $appointmentTime,
            "goods_info" => [
                "is_protect_price" => isset($data['insuredMark']) && $data['insuredMark'] == 1,
                "price" => isset($data['totalValue']) ? $data['totalValue'] : 0,
                "weight" => $data['totalWeight']/1000,
            ],
            "start_point" => [
                "mode" => 1, // 指定地址
                "lng" => $senderLng,
                "lat" => $senderLat,
            ],
            "end_point" => [
                "address_id" => 0, // 青云不使用地址簿
                "lng" => $recipientLng,
                "lat" => $recipientLat,
            ],
        ];

        $res = $service->preOrder($userId, $preOrderParams);
        return [
            'predictDeliveryTime' => strtotime($res["normal"]["estimated_delivery_time"]),
            'actualFee' => floatval($res["normal"]["total_amount"]),
            'deliveryFee' => floatval($res["normal"]["total_amount"]), // 青云的 deliveryFee 等于 actualFee（不含保价费）
            'deliveryDistance' => intval($res['distance']),
            'discountFee' => 0.00, // 暂不支持优惠
            'insuredFee' => 0.00, // 保价费在 actualFee 中已包含
        ];
    }

    /**
     * 发单接口 - 创建配送订单
     *
     * @param array $data 发单请求数据
     * @return array 发单结果
     * @throws \Exception
     */
    public function send(array $data): array
    {
        Log::channel('qingyun')->info('青云发单请求处理开始', $data);

        try {
            // 1. 查找用户ID
            $merchant = $this->findByCarrierMerchantId($data['carrierShopId']);
            if (!$merchant) {
                return [
                    'code' => self::RESULT_PARAM_ERROR,
                    'message' => '配送商ID不存在',
                    'data' => null
                ];
            }

            $userId = $merchant->user_id;
            $merchantId = $merchant->id;


            // 3. 准备预约时间
            $appointmentTime = $this->prepareAppointmentTime($data);

            // 4. 准备地址信息
            $addressInfo = $this->prepareAddressInfo($userId, $data);

            // 5. 创建订单
            $order = $this->createOrderWithPreparedData(
                $userId,
                $data,
                $appointmentTime,
                $addressInfo['startAddress'],
                $addressInfo['endAddress'],
                $merchantId
            );

            // 6. 格式化返回结果
            $result = $this->formatOrderResultData($order);

            Log::channel('qingyun')->info('青云发单成功', [
                'order_id' => $order->id,
                'order_no' => $order->order_no,
                'result' => $result
            ]);

            return [
                'code' => self::RESULT_SUCCESS,
                'message' => '成功',
                'data' => $result
            ];

        } catch (\Exception $e) {
            Log::channel('qingyun')->error('青云发单失败', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'data' => $data
            ]);

            return [
                'code' => self::RESULT_SYSTEM_ERROR,
                'message' => '系统错误: ' . $e->getMessage(),
                'data' => null
            ];
        }
    }

    /**
     * 准备预约时间
     *
     * @param array $data
     * @return string|null
     */
    private function prepareAppointmentTime(array $data): ?string
    {
        return isset($data['appointmentTime']) && $data['appointmentTime'] > 0
            ? date('Y-m-d H:i:s', $data['appointmentTime'])
            : null;
    }

    /**
     * 准备地址信息
     *
     * @param int $userId
     * @param array $data
     * @return array
     * @throws \Exception
     */
    private function prepareAddressInfo(int $userId, array $data): array
    {
        // 转换经纬度格式（青云传入的是整数，需要转换为小数）
        $senderLng = $data["senderLng"]/1000000;
        $senderLat = $data["senderLat"]/1000000;
        $recipientLng = $data["recipientLng"]/1000000;
        $recipientLat = $data["recipientLat"]/1000000;

        // 创建或获取发件人地址
        $startAddress = $this->createOrGetAddress($userId, [
            'name' => $data['senderName'] ?? '发件人',
            'phone' => $data['senderPhone'] ?? '',
            'address' => $data['senderAddress'] ?? '',
            'lng' => $senderLng,
            'lat' => $senderLat,
        ]);

        // 创建或获取收件人地址
        $endAddress = $this->createOrGetAddress($userId, [
            'name' => $data['recipientName'] ?? '收件人',
            'phone' => $data['recipientPhone'] ?? '',
            'address' => $data['recipientAddress'] ?? '',
            'lng' => $recipientLng,
            'lat' => $recipientLat,
        ]);

        return [
            'startAddress' => $startAddress,
            'endAddress' => $endAddress
        ];
    }

    /**
     * 创建或获取地址
     *
     * @param int $userId
     * @param array $addressData
     * @return UserAddress
     * @throws \Exception
     */
    private function createOrGetAddress(int $userId, array $addressData): UserAddress
    {
        // 查找是否已存在相同的地址
        $existingAddress = UserAddress::where('user_id', $userId)
            ->where('lng', $addressData['lng'])
            ->where('lat', $addressData['lat'])
            ->first();

        if ($existingAddress) {
            return $existingAddress;
        }

        // 创建新地址
        return UserAddress::create([
            'user_id' => $userId,
            'name' => $addressData['name'],
            'phone' => $addressData['phone'],
            'address' => $addressData['address'],
            'lng' => $addressData['lng'],
            'lat' => $addressData['lat'],
            'is_default' => 0,
        ]);
    }

    /**
     * 使用准备好的数据创建订单
     *
     * @param int $userId
     * @param array $data
     * @param string|null $appointmentTime
     * @param UserAddress $startAddress
     * @param UserAddress $endAddress
     * @param int $merchantId
     * @return O2oErrandOrder
     * @throws \Exception
     */
    private function createOrderWithPreparedData(
        int $userId,
        array $data,
        ?string $appointmentTime,
        UserAddress $startAddress,
        UserAddress $endAddress,
        int $merchantId
    ): O2oErrandOrder {
        $service = new O2oErrandOrderService();

        // 准备订单参数
        $orderParams = [
            "type" => 1, // 普通配送
            "coupon_id" => 0,
            "gratuity" => 0, // 青云暂不支持小费
            "appointment_time" => $appointmentTime,
            "goods_info" => [
                "is_protect_price" => isset($data['insuredMark']) && $data['insuredMark'] == 1,
                "price" => isset($data['totalValue']) ? $data['totalValue'] : 0,
                "weight" => $data['totalWeight']/1000,
                "goods_detail" => $data['goodsDetail'] ?? '青云配送商品',
            ],
            "start_point" => [
                "mode" => 1, // 指定地址
                "address_id" => $startAddress->id,
                "lng" => $startAddress->lng,
                "lat" => $startAddress->lat,
            ],
            "end_point" => [
                "address_id" => $endAddress->id,
                "lng" => $endAddress->lng,
                "lat" => $endAddress->lat,
            ],
            "remark" => $data['remark'] ?? '',
        ];

        // 创建订单
        $order = $service->createOrder($userId, $orderParams);

        // 更新订单的外部订单号和应用标识
        $order->update([
            'out_order_no' => $data['orderId'],
            'app_key' => O2oErrandOrder::APP_KEY_QY,
            'merchant_id' => $merchantId,
        ]);

        return $order;
    }

    /**
     * 格式化订单结果数据
     *
     * @param O2oErrandOrder $order
     * @return array
     */
    public function formatOrderResultData(O2oErrandOrder $order): array
    {
        // 计算实际支付金额（分转元）
        $actualFee = floatval(fentoyuan($order->actual_amount + $order->gratuity + $order->goods_protected_price));

        // 配送费（不含保价费和小费）
        $deliveryFee = floatval(fentoyuan($order->actual_amount));

        // 小费金额
        $tipFee = floatval(fentoyuan($order->gratuity));

        // 保价费
        $insuredFee = floatval(fentoyuan($order->goods_protected_price));

        return [
            'orderId' => $order->out_order_no,
            'carrierDeliveryId' => $order->order_no,
            'predictDeliveryTime' => $order->estimated_delivery_time->timestamp,
            'actualFee' => $actualFee,
            'deliveryFee' => $deliveryFee,
            'deliveryDistance' => $order->distance,
            'discountFee' => 0.0,
            'insuredFee' => $insuredFee,
            'tipFee' => $tipFee,
        ];
    }

    /**
     * 取消订单接口 - 参考maiyatian的取消配送实现
     *
     * @param array $data 取消订单请求数据
     * @return array 取消订单结果
     * @throws \Exception
     */
    public function cancelOrder(array $data): array
    {
        Log::channel('qingyun')->info('青云取消订单请求处理开始', $data);

        try {
            // 1. 查找订单
            $order = O2oErrandOrder::where('out_order_no', $data['orderId'])
                ->where('app_key', O2oErrandOrder::APP_KEY_QY)
                ->first();

            if (!$order) {
                return [
                    'code' => self::RESULT_ORDER_NOT_EXIST,
                    'message' => '订单不存在',
                    'data' => null
                ];
            }

            // 2. 检查订单是否已完成，已完成的订单不能取消
            if ($order->order_status == O2oErrandOrder::STATUS_FINISH) {
                Log::channel('qingyun')->warning('青云取消订单失败（订单已完成）', [
                    'order_id' => $order->id,
                    'order_no' => $order->order_no,
                    'order_status' => $order->order_status
                ]);

                return [
                    'code' => self::RESULT_ORDER_COMPLETED,
                    'message' => '订单已完成，不能取消',
                    'data' => null
                ];
            }

            // 3. 检查订单是否已经取消（幂等性处理）
            if ($order->refund_status != O2oErrandOrder::REFUND_STATUS_INIT) {
                // 订单已经取消，返回取消费用信息（幂等性）
                $cancelFee = $this->calculateCancelFee($order);

                Log::channel('qingyun')->info('青云取消订单成功（订单已取消）', [
                    'order_id' => $order->id,
                    'order_no' => $order->order_no,
                    'refund_status' => $order->refund_status,
                    'cancel_fee' => $cancelFee
                ]);

                return [
                    'code' => self::RESULT_SUCCESS,
                    'message' => '成功',
                    'data' => [
                        'cancelFee' => $cancelFee
                    ]
                ];
            }

            // 4. 构建取消原因 - 参考maiyatian的实现
            $cancelReason = $this->buildCancelReason($data);

            // 5. 调用 O2oErrandOrderService 的 refundOrder 方法进行退款 - 完全按照maiyatian的方式
            $orderService = new O2oErrandOrderService();
            $refundedOrder = $orderService->refundOrder($order->order_no, $cancelReason);

            $cancelFee = $this->calculateCancelFee($refundedOrder);

            Log::channel('qingyun')->info('青云取消订单成功', [
                'order_id' => $order->id,
                'order_no' => $order->order_no,
                'cancel_reason' => $cancelReason,
                'cancel_fee' => $cancelFee
            ]);

            return [
                'code' => self::RESULT_SUCCESS,
                'message' => '成功',
                'data' => [
                    'cancelFee' => $cancelFee
                ]
            ];

        } catch (\Exception $e) {
            Log::channel('qingyun')->error('青云取消订单失败', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'data' => $data
            ]);

            return [
                'code' => self::RESULT_SYSTEM_ERROR,
                'message' => '系统错误: ' . $e->getMessage(),
                'data' => null
            ];
        }
    }

    /**
     * 计算取消费用
     *
     * @param O2oErrandOrder $order
     * @return float
     */
    private function calculateCancelFee(O2oErrandOrder $order): float
    {
        // 取消费用 = 实际支付金额 - 退款金额
        $actualAmount = floatval(fentoyuan($order->actual_amount + $order->gratuity + $order->goods_protected_price));
        $refundAmount = floatval(fentoyuan($order->refund_amount));
        return max(0, $actualAmount - $refundAmount);
    }

    /**
     * 构建取消原因
     *
     * @param array $data
     * @return string
     */
    private function buildCancelReason(array $data): string
    {
        $reasonCode = $data['cancelReasonCode'] ?? 1;
        $reasonDesc = $data['cancelReasonDesc'] ?? '青云平台取消';

        // 根据青云的取消原因映射
        $reasonMapping = config('qingyun.cancel_reason_mapping', [
            1 => "商户取消",
            2 => "用户在渠道或更上一层的取消",
            3 => "因为各系统原因发起的取消",
            4 => "配送商的骑手发起的取消",
            5 => "配送商侧发起的取消配送",
            99 => "其他原因",
        ]);

        $mappedReason = $reasonMapping[$reasonCode] ?? "其他原因";
        return "青云平台取消：{$mappedReason} - {$reasonDesc}";
    }

    /**
     * 获取订单详情接口
     *
     * @param array $data 订单详情请求数据
     * @return array 订单详情结果
     * @throws \Exception
     */
    public function getOrderDetail(array $data): array
    {
        Log::channel('qingyun')->info('青云订单详情请求处理开始', $data);

        try {
            // 1. 查找订单
            $order = O2oErrandOrder::where('out_order_no', $data['orderId'])
                ->where('app_key', O2oErrandOrder::APP_KEY_QY)
                ->first();

            if (!$order) {
                return [
                    'code' => self::RESULT_ORDER_NOT_EXIST,
                    'message' => '订单不存在',
                    'data' => null
                ];
            }

            // 2. 格式化订单详情数据
            $orderDetail = $this->formatOrderDetailData($order);

            Log::channel('qingyun')->info('青云订单详情查询成功', [
                'order_id' => $order->id,
                'order_no' => $order->order_no,
                'status' => $order->order_status
            ]);

            return [
                'code' => self::RESULT_SUCCESS,
                'message' => '成功',
                'data' => $orderDetail
            ];

        } catch (\Exception $e) {
            Log::channel('qingyun')->error('青云订单详情查询失败', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'data' => $data
            ]);

            return [
                'code' => self::RESULT_SYSTEM_ERROR,
                'message' => '系统错误: ' . $e->getMessage(),
                'data' => null
            ];
        }
    }

    /**
     * 格式化订单详情数据
     *
     * @param O2oErrandOrder $order
     * @return array
     */
    private function formatOrderDetailData(O2oErrandOrder $order): array
    {
        // 获取青云状态码
        $qingyunStatus = $this->getQingyunStatus($order);

        // 计算各种费用（分转元）
        $actualFee = floatval(fentoyuan($order->actual_amount + $order->gratuity + $order->goods_protected_price));
        $deliveryFee = floatval(fentoyuan($order->actual_amount));
        $tipFee = floatval(fentoyuan($order->gratuity));
        $insuredFee = floatval(fentoyuan($order->goods_protected_price));
        $discountFee = floatval(fentoyuan($order->coupon_amount));

        // 基础订单详情数据
        $orderDetail = [
            'orderId' => $order->out_order_no,
            'carrierDeliveryId' => $order->order_no,
            'status' => $qingyunStatus,
            'operateTime' => $order->updated_at->timestamp,
            'createOrderTime' => $order->create_time->timestamp,
            'actualFee' => $actualFee,
            'discountFee' => $discountFee,
            'insuredFee' => $insuredFee,
            'deliveryFee' => $deliveryFee,
            'deliveryDistance' => $order->distance,
        ];

        // 如果有骑手信息，添加骑手相关字段
        if ($order->rider_id) {
            $rider = \App\Models\Rider::find($order->rider_id);
            if ($rider) {
                $orderDetail['riderName'] = $rider->name;
                $orderDetail['riderPhone'] = $rider->phone;
            }
        }

        return $orderDetail;
    }

    /**
     * 获取青云状态码
     *
     * @param O2oErrandOrder $order
     * @return int
     */
    public function getQingyunStatus(O2oErrandOrder $order): int
    {
        return self::STATUS_TO_QY_MAP[$order->order_status] ?? self::QY_STATUS_CREATED;
    }

    /**
     * 骑手/司机经纬度查询接口 - 参考MaiYaTian的rider_location实现
     *
     * @param array $data 查询请求数据
     * @return array 查询结果
     * @throws \Exception
     */
    public function getRiderLocation(array $data): array
    {
        Log::channel('qingyun')->info('青云骑手位置查询请求处理开始', $data);

        try {
            // 1. 查找订单
            $order = O2oErrandOrder::where('out_order_no', $data['orderId'])
                ->where('app_key', O2oErrandOrder::APP_KEY_QY)
                ->first();

            if (!$order) {
                return [
                    'code' => self::RESULT_ORDER_NOT_EXIST,
                    'message' => '订单不存在',
                    'data' => null
                ];
            }

            // 2. 构建基础返回数据
            $result = [
                'orderId' => $order->out_order_no,
                'carrierDeliveryId' => $order->order_no,
                'riderLng' => 0,
                'riderLat' => 0,
            ];

            // 3. 如果有骑手，获取骑手位置信息
            if ($order->rider_id) {
                $riderLocationService = new \App\Services\RiderLocationService();
                $location = $riderLocationService->getRiderLocation($order->rider_id);

                if ($location) {
                    // 青云需要返回整数格式的经纬度（乘以1000000）
                    $result['riderLng'] = intval($location['lng'] * 1000000);
                    $result['riderLat'] = intval($location['lat'] * 1000000);
                }
            }

            Log::channel('qingyun')->info('青云骑手位置查询成功', [
                'order_id' => $order->id,
                'order_no' => $order->order_no,
                'rider_id' => $order->rider_id,
                'result' => $result
            ]);

            return [
                'code' => self::RESULT_SUCCESS,
                'message' => '成功',
                'data' => $result
            ];

        } catch (\Exception $e) {
            Log::channel('qingyun')->error('青云骑手位置查询失败', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'data' => $data
            ]);

            return [
                'code' => self::RESULT_SYSTEM_ERROR,
                'message' => '系统错误: ' . $e->getMessage(),
                'data' => null
            ];
        }
    }

    /**
     * 添加小费接口
     *
     * @param array $data 请求数据
     * @return array 处理结果
     * @throws \Exception
     */
    public function addTip(array $data): array
    {
        try {
            Log::channel('qingyun')->info('青云添加小费处理开始', $data);

            $orderId = $data['orderId'];
            $tipFee = floatval($data['tipFee']);

            // 查找订单
            $order = O2oErrandOrder::where('out_order_no', $orderId)
                ->where('app_key', O2oErrandOrder::APP_KEY_QY)
                ->first();

            if (!$order) {
                Log::channel('qingyun')->warning('青云订单不存在', ['order_id' => $orderId]);
                return [
                    'code' => self::RESULT_ORDER_NOT_EXIST,
                    'message' => '订单不存在',
                    'data' => null
                ];
            }

            // 将元转换为分进行处理（与maiyatian保持一致）
            $tipFeeInFen = yuantofen($tipFee);

            // 调用小费处理逻辑
            // 青云不在商户余额里充值，因此扣款走的是用户余额
            $this->processTips($order, $tipFeeInFen);

            // 按照青云接口规范返回数据
            $resultData = ['tipFee' => $tipFee];

            Log::channel('qingyun')->info('青云添加小费处理成功', [
                'order_id' => $orderId,
                'tip_fee' => $tipFee,
                'tip_fee_fen' => $tipFeeInFen
            ]);

            return [
                'code' => self::RESULT_SUCCESS,
                'message' => '成功',
                'data' => $resultData
            ];

        } catch (BusinessException $e) {
            Log::channel('qingyun')->warning('青云添加小费业务异常', [
                'error' => $e->getMessage(),
                'data' => $data
            ]);

            // 根据错误信息选择合适的错误码
            $errorCode = self::RESULT_PARAM_ERROR;
            if (strpos($e->getMessage(), '余额不足') !== false) {
                $errorCode = self::RESULT_INSUFFICIENT_BALANCE;
            } elseif (strpos($e->getMessage(), '已接单') !== false) {
                $errorCode = self::RESULT_RIDER_ACCEPTED;
            } elseif (strpos($e->getMessage(), '小费金额已至上限') !== false) {
                $errorCode = self::RESULT_TIP_LIMIT;
            }

            return [
                'code' => $errorCode,
                'message' => $e->getMessage(),
                'data' => null
            ];

        } catch (\Exception $e) {
            Log::channel('qingyun')->error('青云添加小费系统异常', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'data' => $data
            ]);

            return [
                'code' => self::RESULT_SYSTEM_ERROR,
                'message' => '系统错误: ' . $e->getMessage(),
                'data' => null
            ];
        }
    }

    /**
     * 处理小费逻辑（参考maiyatian实现）
     *
     * @param O2oErrandOrder $order 订单
     * @param int $amount 小费金额（分）
     * @return array 处理结果
     * @throws \Exception
     */
    private function processTips(O2oErrandOrder $order, $amount): array
    {
        if ($amount <= 0) {
            return [];
        }

        // 基本状态检查
        if ($order->order_status == O2oErrandOrder::STATUS_CANCEL) {
            throw new \Exception("该订单已取消");
        }
        if ($order->order_status < O2oErrandOrder::STATUS_PAID || $order->order_status >= O2oErrandOrder::STATUS_FINISH) {
            throw new \Exception("该订单未支付或已完成");
        }

        // 判断是否为青云订单
        if ($order->app_key == O2oErrandOrder::APP_KEY_QY) {
            // 青云订单 - 从商家余额扣除
            return $this->processQingyunMerchantTips($order, $amount);
        } else {
            // 普通订单 - 从用户余额扣除
            return $this->processUserTips($order, $amount);
        }
    }

    /**
     * 处理青云商家小费
     *
     * @param O2oErrandOrder $order
     * @param int $amount
     * @return array
     * @throws \Exception
     */
    private function processQingyunMerchantTips(O2oErrandOrder $order, int $amount): array
    {
        // 查找商家
        $merchant = Merchant::find($order->merchant_id);
        if (!$merchant) {
            throw new \Exception("商家不存在");
        }

        // 检查商家余额
        if ($merchant->balance < $amount) {
            throw new \Exception("商家余额不足");
        }

        DB::beginTransaction();
        try {
            // 扣除商家余额
            $merchant->decrement('balance', $amount);

            // 增加订单小费
            $order->increment('gratuity', $amount);

            // 记录商家账户流水
            MerchantAccountLog::create([
                'merchant_id' => $merchant->id,
                'type' => MerchantAccountLog::TYPE_EXPENSE,
                'amount' => $amount,
                'balance' => $merchant->balance,
                'description' => "青云订单小费：{$order->order_no}",
                'order_id' => $order->id,
            ]);

            DB::commit();

            Log::channel('qingyun')->info('青云商家小费处理成功', [
                'merchant_id' => $merchant->id,
                'order_id' => $order->id,
                'amount' => $amount,
                'merchant_balance' => $merchant->balance
            ]);

            return [
                'merchant_id' => $merchant->id,
                'amount' => $amount,
                'balance' => $merchant->balance
            ];

        } catch (\Exception $e) {
            DB::rollBack();
            throw $e;
        }
    }

    /**
     * 处理用户小费（普通订单）
     *
     * @param O2oErrandOrder $order
     * @param int $amount
     * @return array
     * @throws \Exception
     */
    private function processUserTips(O2oErrandOrder $order, int $amount): array
    {
        // 查找用户账户
        $userAccount = UserAccount::where('user_id', $order->user_id)->first();
        if (!$userAccount) {
            throw new \Exception("用户账户不存在");
        }

        // 检查用户余额
        if ($userAccount->balance < $amount) {
            throw new \Exception("用户余额不足");
        }

        DB::beginTransaction();
        try {
            // 扣除用户余额
            $userAccount->decrement('balance', $amount);

            // 增加订单小费
            $order->increment('gratuity', $amount);

            // 记录用户账户流水
            UserAccountFlow::create([
                'user_id' => $order->user_id,
                'type' => UserAccountFlow::TYPE_EXPENSE,
                'amount' => $amount,
                'balance' => $userAccount->balance,
                'description' => "订单小费：{$order->order_no}",
                'order_id' => $order->id,
            ]);

            DB::commit();

            Log::channel('qingyun')->info('用户小费处理成功', [
                'user_id' => $order->user_id,
                'order_id' => $order->id,
                'amount' => $amount,
                'user_balance' => $userAccount->balance
            ]);

            return [
                'user_id' => $order->user_id,
                'amount' => $amount,
                'balance' => $userAccount->balance
            ];

        } catch (\Exception $e) {
            DB::rollBack();
            throw $e;
        }
    }

}
