<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;

/**
 * 青云API签名验证中间件
 *
 * 签名协议：
 * 1. 将所有系统参数及业务参数（其中sign，byte[]及值为空的参数除外）按照参数名的字典顺序排序
 * 2. 将参数以参数1值1参数2值2...的顺序拼接，例如a=&c=3&b=1，变为b1c3，参数使用utf-8编码
 * 3. 按照secret + 排序后的参数的顺序进行连接，得到加密前的字符串
 * 4. 对加密前的字符串进行sha1加密并转为小写字符串，得到签名
 * 5. 将得到的签名赋给sign作为请求的参数
 */
class VerifyQingyunSignature
{
    /**
     * 处理传入的请求
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure  $next
     * @return mixed
     */
    public function handle(Request $request, Closure $next)
    {
        try {
            // 记录验签请求
            Log::channel('qingyun')->info('青云API验签开始', [
                'url' => $request->url(),
                'method' => $request->method(),
                'params' => $request->all()
            ]);

            // 验证必需的系统参数
            $validation = $this->validateRequiredParams($request);
            if ($validation !== true) {
                return $validation;
            }

            // 验证签名
            $signatureValidation = $this->validateSignature($request);
            if ($signatureValidation !== true) {
                return $signatureValidation;
            }

            // 验证时间戳（可选：防重放攻击）
            $timestampValidation = $this->validateTimestamp($request);
            if ($timestampValidation !== true) {
                return $timestampValidation;
            }

            Log::channel('qingyun')->info('青云API验签成功');

            return $next($request);

        } catch (\Exception $e) {
            Log::channel('qingyun')->error('青云API验签异常', [
                'message' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'request_data' => $request->all()
            ]);

            return $this->errorResponse('系统错误');
        }
    }

    /**
     * 验证必需的系统参数
     *
     * @param Request $request
     * @return mixed
     */
    private function validateRequiredParams(Request $request)
    {
        $requiredParams = ['developerId', 'timestamp', 'version', 'sign'];

        foreach ($requiredParams as $param) {
            if (!$request->has($param) || empty($request->input($param))) {
                Log::channel('qingyun')->warning('青云API验签失败：缺少必需参数', [
                    'missing_param' => $param,
                    'provided_params' => array_keys($request->all())
                ]);

                return $this->errorResponse('缺少必需参数: ' . $param);
            }
        }

        // 验证developerId
        $developerId = $request->input('developerId');
        $configDeveloperId = config('qingyun.developer_id');

        if ($developerId !== $configDeveloperId) {
            Log::channel('qingyun')->warning('青云API验签失败：developerId无效', [
                'provided_developer_id' => $developerId,
                'expected_developer_id' => $configDeveloperId
            ]);

            return $this->errorResponse('developerId无效');
        }

        // 验证version
        $version = $request->input('version');
        if ($version !== '1.0') {
            Log::channel('qingyun')->warning('青云API验签失败：不支持的API版本', [
                'provided_version' => $version
            ]);

            return $this->errorResponse('不支持的API版本');
        }

        return true;
    }

    /**
     * 验证签名
     *
     * @param Request $request
     * @return mixed
     */
    private function validateSignature(Request $request)
    {
        $providedSign = $request->input('sign');
        $calculatedSign = $this->calculateSignature($request);

        if ($providedSign !== $calculatedSign) {
            Log::channel('qingyun')->warning('青云API验签失败：签名不匹配', [
                'provided_sign' => $providedSign,
                'calculated_sign' => $calculatedSign,
                'request_params' => $request->all()
            ]);

            return $this->errorResponse('签名验证失败');
        }

        return true;
    }

    /**
     * 计算签名
     *
     * @param Request $request
     * @return string
     */
    private function calculateSignature(Request $request): string
    {
        $params = $request->all();

        // 移除sign参数和空值参数
        unset($params['sign']);
        $params = array_filter($params, function($value) {
            return $value !== null && $value !== '';
        });

        // 按参数名字典顺序排序
        ksort($params);

        // 拼接参数
        $paramString = '';
        foreach ($params as $key => $value) {
            $paramString .= $key . $value;
        }

        // 加上secret前缀
        $secret = config('qingyun.secret');
        $signString = $secret . $paramString;

        // SHA1加密并转小写
        $signature = strtolower(sha1($signString));

        Log::channel('qingyun')->debug('青云签名计算过程', [
            'params' => $params,
            'param_string' => $paramString,
            'sign_string' => $signString,
            'signature' => $signature
        ]);

        return $signature;
    }

    /**
     * 验证时间戳（防重放攻击）
     *
     * @param Request $request
     * @return mixed
     */
    private function validateTimestamp(Request $request)
    {
        $timestamp = $request->input('timestamp');
        $currentTime = time();
        $timeDiff = abs($currentTime - $timestamp);

        // 允许5分钟的时间差
        $maxTimeDiff = 300;

        if ($timeDiff > $maxTimeDiff) {
            Log::channel('qingyun')->warning('青云API验签失败：时间戳超出允许范围', [
                'provided_timestamp' => $timestamp,
                'current_timestamp' => $currentTime,
                'time_diff' => $timeDiff,
                'max_allowed_diff' => $maxTimeDiff
            ]);

            return $this->errorResponse('请求时间戳无效');
        }

        return true;
    }

    /**
     * 返回错误响应
     *
     * @param string $message
     * @return \Illuminate\Http\JsonResponse
     */
    private function errorResponse(string $message)
    {
        return response()->json([
            'code' => 3, // 青云规范：3表示签名验证失败
            'message' => $message,
            'data' => null
        ]);
    }
}
