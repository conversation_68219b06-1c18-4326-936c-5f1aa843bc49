<?php

namespace App\Console\Commands;

use App\Models\O2oErrandOrder;
use App\Models\Rider;
use App\Services\QingyunService;
use App\Services\RiderLocationService;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;

class QingyunCallback extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'qingyun:callback {order_no : 订单号}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = '手动触发青云配送状态回调';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $orderNo = $this->argument('order_no');

        $this->info("开始处理订单号: {$orderNo}");

        try {
            // 查找订单
            $order = O2oErrandOrder::query()->where('out_order_no', $orderNo)->first();

            if (!$order) {
                $this->error("订单不存在: {$orderNo}");
                return Command::FAILURE;
            }

            $this->info("找到订单: {$order->order_no}");
            $this->info("订单状态: " . (O2oErrandOrder::StatusMap[$order->order_status] ?? '未知'));
            $this->info("应用标识: {$order->app_key}");

            // 检查是否为青云订单
            if ($order->app_key !== O2oErrandOrder::APP_KEY_QY) {
                $this->error("该订单不是青云订单，app_key: {$order->app_key}");
                return Command::FAILURE;
            }

            // 获取骑手信息
            $rider = null;
            if ($order->rider_id) {
                $rider = Rider::query()->where("id", $order->rider_id)->first();
                if ($rider) {
                    $this->info("骑手信息: {$rider->name} ({$rider->phone})");
                } else {
                    $this->warn("未找到骑手信息，rider_id: {$order->rider_id}");
                }
            } else {
                $this->warn("订单未分配骑手");
            }

            // 构建其他数据
            $otherData = [];

            // 如果有骑手，添加骑手信息
            if ($rider) {
                $otherData['rider_name'] = $rider->name;
                $otherData['rider_phone'] = $rider->phone;

                // 获取骑手位置信息
                $riderLocationService = new RiderLocationService();
                $location = $riderLocationService->getRiderLocation($rider->id);
                if ($location) {
                    $otherData['rider_lng'] = $location['lng'];
                    $otherData['rider_lat'] = $location['lat'];
                }
            }

            // 添加费用信息
            $otherData['actual_fee'] = floatval(fentoyuan($order->actual_amount + $order->gratuity + $order->goods_protected_price));
            $otherData['delivery_fee'] = floatval(fentoyuan($order->actual_amount));
            $otherData['tip_fee'] = floatval(fentoyuan($order->gratuity));
            $otherData['insured_fee'] = floatval(fentoyuan($order->goods_protected_price));

            $this->info("其他数据: " . json_encode($otherData, JSON_UNESCAPED_UNICODE));

            // 获取青云状态码
            $qingyunService = new QingyunService();
            $qingyunStatus = $qingyunService->getQingyunStatus($order);

            $this->info("青云订单状态: {$qingyunStatus}");

            // 执行青云配送状态回调
            $this->info("正在执行青云配送状态回调...");

            // 注意：这里需要实现deliveryStatusCallback方法，类似海博的实现
            // 暂时输出提示信息
            $this->info("青云配送状态回调功能需要进一步实现");
            $this->info("订单信息已准备完成，可以进行回调");

            return Command::SUCCESS;

        } catch (\Exception $e) {
            $this->error("处理过程中发生异常: " . $e->getMessage());
            $this->error("异常堆栈: " . $e->getTraceAsString());

            Log::channel('qingyun')->error('青云回调命令执行异常', [
                'order_no' => $orderNo,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return Command::FAILURE;
        }
    }
}
