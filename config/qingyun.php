<?php

return [
    /*
    |--------------------------------------------------------------------------
    | 青云配送平台配置
    |--------------------------------------------------------------------------
    |
    | 这里配置青云配送平台相关的参数
    |
    */

    // 青云回调地址
    'callback_url' => env('QINGYUN_CALLBACK_URL', ''),

    // 青云调用协议配置
    'developer_id' => env('QINGYUN_DEVELOPER_ID', ''),
    'secret' => env('QINGYUN_SECRET', ''),

    // 青云环境配置
    'environment' => env('QINGYUN_ENVIRONMENT', 'test'), // test, staging, production
    'base_urls' => [
        'test' => 'https://pre-carrieropen.qingyuntech.com',
        'staging' => 'https://carrieropen-tool.qingyuntech.com',
        'production' => 'https://carrieropen.qingyuntech.com',
    ],

    // 状态映射配置
    'status_mapping' => [
        'created' => 10,        // 已创建(待接单)
        'accepted' => 20,       // 骑手已接单
        'arrived_store' => 25,  // 已到店
        'picked_up' => 30,      // 已取货
        'delivered' => 50,      // 已送达
        'cancelled' => 99,      // 已取消
    ],

    // 取消原因映射
    'cancel_reason_mapping' => [
        1 => "商户取消",
        2 => "用户在渠道或更上一层的取消",
        3 => "因为各系统原因发起的取消",
        4 => "配送商的骑手发起的取消",
        5 => "配送商侧发起的取消配送",
        99 => "其他原因",
    ],

    // 商品分类映射（青云分类到系统分类）
    'goods_category_mapping' => [
        100 => 5,   // 美食 -> 餐饮美食
        104 => 7,   // 生鲜果蔬 -> 生鲜果蔬
        108 => 14,  // 医药健康 -> 医药健康
        105 => 14,  // 超市百货 -> 超市百货
        103 => 9,   // 鲜花绿植 -> 鲜花绿植
        102 => 8,   // 烘焙蛋糕 -> 烘焙蛋糕
        101 => 5,   // 饮品奶茶 -> 饮品奶茶
        999 => 14,  // 其他 -> 其他
    ],

    // 订单来源映射
    'order_source_mapping' => [
        300 => '青云｜京东',
        100 => '青云｜美团',
        200 => '青云｜饿了么',
        400 => '青云自营',
        600 => '青云｜有赞',
        700 => '青云｜抖音',
        800 => '青云｜淘宝买菜',
    ],
];
