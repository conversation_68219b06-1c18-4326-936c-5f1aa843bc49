# 青云询价接口实现文档

## 概述

本文档描述了为青云系统实现的询价接口，该接口用于获取预估配送费、配送时间等信息。

## 接口信息

- **接口地址**: `POST /api/qingyun/valuating`
- **Content-Type**: `application/json`
- **功能**: 询价，获取预估配送费、配送时间等信息

## 实现文件

### 1. 控制器文件
- **文件**: `app/Http/Controllers/Api/QingyunController.php`
- **方法**: `valuating(Request $request)`
- **功能**: 处理询价请求，验证参数，调用服务层逻辑

### 2. 服务文件
- **文件**: `app/Services/QingyunService.php`
- **方法**: `valuating(array $data)`
- **功能**: 核心询价业务逻辑

### 3. 路由配置
- **文件**: `routes/api.php`
- **路由**: `Route::post('valuating', [QingyunController::class, 'valuating'])`

## 请求参数

### 必填参数

| 参数名 | 类型 | 说明 | 示例 |
|--------|------|------|------|
| orderId | string | 青云平台订单号 | "QY20231201001" |
| carrierShopId | string | 配送商门店ID | "QY_123" |
| senderLng | integer | 发件人经度（*1000000） | 116397128 |
| senderLat | integer | 发件人纬度（*1000000） | 39916527 |
| recipientLng | integer | 收件人经度（*1000000） | 116407128 |
| recipientLat | integer | 收件人纬度（*1000000） | 39926527 |

### 可选参数

| 参数名 | 类型 | 说明 | 示例 |
|--------|------|------|------|
| appointmentTime | integer | 预约时间戳 | 1701417600 |
| totalWeight | integer | 商品总重量（克） | 1000 |
| totalValue | float | 商品总价值（元） | 100.00 |
| insuredMark | integer | 是否保价（1:是 0:否） | 1 |

## 响应格式

### 成功响应

```json
{
    "code": 0,
    "message": "成功",
    "data": {
        "predictDeliveryTime": 1701421200,
        "actualFee": 12.50,
        "deliveryFee": 12.50,
        "deliveryDistance": 3500,
        "discountFee": 0.00,
        "insuredFee": 0.00
    }
}
```

### 响应字段说明

| 字段名 | 类型 | 说明 |
|--------|------|------|
| predictDeliveryTime | integer | 预计送达时间戳 |
| actualFee | float | 实际费用（元） |
| deliveryFee | float | 配送费（元） |
| deliveryDistance | integer | 配送距离（米） |
| discountFee | float | 优惠费用（元） |
| insuredFee | float | 保价费用（元） |

### 错误响应

```json
{
    "code": 2,
    "message": "参数验证失败: 青云平台订单号不能为空",
    "data": null
}
```

## 错误码说明

| 错误码 | 说明 |
|--------|------|
| 0 | 成功 |
| 1 | 系统异常 |
| 2 | 参数错误 |
| 5 | 请求频率限制 |
| 10 | 该地区暂无运力 |

## 实现特点

1. **完全复制海博逻辑**: 青云接口的实现完全基于海博接口，保持相同的业务逻辑和处理流程
2. **独立配置**: 青云有独立的配置文件 `config/qingyun.php`
3. **独立日志**: 使用 `qingyun` 日志通道记录相关日志
4. **限流保护**: 实现了30次/秒的请求限流
5. **运力检查**: 支持发件人和收件人地址的运力覆盖检查
6. **订单查询**: 支持已有订单的查询和新订单的询价

## 配置说明

在 `.env` 文件中添加青云相关配置：

```bash
# 青云配送平台配置
QINGYUN_CALLBACK_URL=https://pre-carrieropen.qingyuntech.com/api/delivery/statusCallback
QINGYUN_DEVELOPER_ID=your_developer_id
QINGYUN_SECRET=your_secret
QINGYUN_ENVIRONMENT=test
```

## 使用示例

```bash
curl -X POST "http://your-domain.com/api/qingyun/valuating" \
  -H "Content-Type: application/json" \
  -d '{
    "orderId": "QY20231201001",
    "carrierShopId": "QY_123",
    "senderLng": 116397128,
    "senderLat": 39916527,
    "recipientLng": 116407128,
    "recipientLat": 39926527,
    "totalWeight": 1000,
    "totalValue": 100.00
  }'
```

## 注意事项

1. 经纬度参数需要乘以1000000传入（整数格式）
2. 接口有限流保护，建议控制请求频率
3. 需要确保发件人和收件人地址在服务覆盖范围内
4. 青云订单使用 `APP_KEY_QY` 标识，门店ID使用 `QY_` 前缀
